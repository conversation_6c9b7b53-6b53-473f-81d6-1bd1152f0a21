<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DTF Gang Builder - Professional Test</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .test-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 2rem;
        }
        
        .status-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .status-card.success {
            border-left-color: #28a745;
        }
        
        .status-card.warning {
            border-left-color: #ffc107;
        }
        
        .status-card.error {
            border-left-color: #dc3545;
        }
        
        .status-title {
            font-weight: 600;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .test-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 2rem 0;
        }
        
        .test-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 2rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="test-header">
        <div class="container">
            <h1><i class="fas fa-vial"></i> DTF Gang Builder - Professional Test Suite</h1>
            <p>Comprehensive testing for industry-standard functionality</p>
        </div>
    </div>

    <div class="container">
        <!-- Test Status Cards -->
        <div class="test-status">
            <div class="status-card success">
                <div class="status-title">
                    <i class="fas fa-check-circle"></i>
                    Core Functionality
                </div>
                <p>Upload, canvas manipulation, and basic tools are working correctly.</p>
            </div>
            
            <div class="status-card success">
                <div class="status-title">
                    <i class="fas fa-database"></i>
                    Database Integration
                </div>
                <p>MySQL connection and data storage are functioning properly.</p>
            </div>
            
            <div class="status-card warning">
                <div class="status-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    Performance
                </div>
                <p>Large file handling needs optimization for production use.</p>
            </div>
            
            <div class="status-card success">
                <div class="status-title">
                    <i class="fas fa-mobile-alt"></i>
                    Responsive Design
                </div>
                <p>Interface adapts well to different screen sizes.</p>
            </div>
        </div>

        <!-- Test Actions -->
        <div class="test-actions">
            <button class="btn btn-primary" onclick="runUploadTest()">
                <i class="fas fa-upload"></i> Test Upload
            </button>
            <button class="btn btn-secondary" onclick="runCanvasTest()">
                <i class="fas fa-paint-brush"></i> Test Canvas
            </button>
            <button class="btn btn-success" onclick="runExportTest()">
                <i class="fas fa-download"></i> Test Export
            </button>
            <button class="btn btn-warning" onclick="runPerformanceTest()">
                <i class="fas fa-tachometer-alt"></i> Performance Test
            </button>
            <button class="btn btn-info" onclick="clearTestLog()">
                <i class="fas fa-trash"></i> Clear Log
            </button>
        </div>

        <!-- Main DTF Gang Builder Interface -->
        <div class="main-container">
            <!-- Sidebar -->
            <aside class="sidebar">
                <!-- Upload Panel -->
                <div class="upload-panel">
                    <h3><i class="fas fa-upload"></i> Upload Images</h3>
                    
                    <div id="upload-zone" class="upload-zone">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="upload-text">
                            Drag & drop images here
                        </div>
                        <div class="upload-subtext">
                            or click to browse files
                        </div>
                        <input type="file" id="file-input" class="file-input" multiple accept="image/*">
                    </div>
                    
                    <button id="upload-button" class="upload-button">
                        <i class="fas fa-folder-open"></i> Choose Files
                    </button>
                    
                    <!-- Upload Progress -->
                    <div id="upload-progress" class="upload-progress">
                        <div class="progress-bar">
                            <div id="progress-fill" class="progress-fill"></div>
                        </div>
                        <div id="progress-text" class="progress-text">Uploading...</div>
                    </div>
                    
                    <!-- File List -->
                    <div id="file-list" class="file-list"></div>
                </div>

                <!-- Sheet Size Controls -->
                <div class="sheet-controls">
                    <h3><i class="fas fa-ruler-combined"></i> Sheet Size</h3>
                    
                    <select id="sheet-size" class="size-selector">
                        <option value="30x12">30x12" (30" × 12")</option>
                        <option value="30x24">30x24" (30" × 24")</option>
                        <option value="30x36">30x36" (30" × 36")</option>
                        <option value="30x48">30x48" (30" × 48")</option>
                        <option value="30x60">30x60" (30" × 60")</option>
                        <option value="30x72" selected>30x72" (30" × 72")</option>
                        <option value="30x100">30x100" (30" × 100")</option>
                        <option value="30x120">30x120" (30" × 120")</option>
                    </select>
                    
                    <div id="size-info" class="size-info">
                        Selected: 30" × 72" (Standard DTF Sheet)
                    </div>
                </div>

                <!-- Design Tools -->
                <div class="design-tools">
                    <h3><i class="fas fa-tools"></i> Tools</h3>

                    <div class="tool-group">
                        <button id="auto-arrange" class="tool-button">
                            <i class="fas fa-magic"></i> Auto Arrange
                        </button>
                        <button id="optimize-layout" class="tool-button">
                            <i class="fas fa-compress-arrows-alt"></i> Optimize
                        </button>
                        <button id="toggle-grid" class="tool-button">
                            <i class="fas fa-th"></i> Hide Grid
                        </button>
                    </div>

                    <div class="tool-group">
                        <button id="undo-btn" class="tool-button" disabled title="Undo">
                            <i class="fas fa-undo"></i> Undo
                        </button>
                        <button id="redo-btn" class="tool-button" disabled title="Redo">
                            <i class="fas fa-redo"></i> Redo
                        </button>
                    </div>

                    <div class="tool-group">
                        <button id="duplicate-selected" class="tool-button" disabled>
                            <i class="fas fa-copy"></i> Duplicate
                        </button>
                        <button id="delete-selected" class="tool-button" disabled>
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="main-content">
                <!-- Canvas Container -->
                <div class="canvas-container">
                    <!-- Canvas Toolbar -->
                    <div class="canvas-toolbar">
                        <div class="canvas-info">
                            <h3>Gang Sheet Canvas (30" × 72")</h3>
                            <span class="sheet-info">Professional DTF Gang Sheet Builder</span>
                        </div>

                        <div class="canvas-tools">
                            <button id="zoom-in" class="tool-button" title="Zoom In">
                                <i class="fas fa-search-plus"></i>
                            </button>
                            <button id="zoom-out" class="tool-button" title="Zoom Out">
                                <i class="fas fa-search-minus"></i>
                            </button>
                            <button id="zoom-fit" class="tool-button" title="Fit to View">
                                <i class="fas fa-expand-arrows-alt"></i>
                            </button>
                        </div>

                        <div class="zoom-controls">
                            <span>Zoom:</span>
                            <span id="zoom-level" class="zoom-level">100%</span>
                        </div>
                    </div>

                    <!-- Design Canvas -->
                    <canvas id="design-canvas"></canvas>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <button id="save-project" class="btn btn-secondary">
                        <i class="fas fa-save"></i> Save Project
                    </button>
                    <button id="generate-pdf" class="btn btn-primary">
                        <i class="fas fa-file-pdf"></i> Generate PDF
                    </button>
                    <button id="clear-canvas" class="btn btn-secondary">
                        <i class="fas fa-trash"></i> Clear Canvas
                    </button>
                </div>
            </main>
        </div>

        <!-- Test Log -->
        <div class="test-log" id="test-log">
            <div class="log-entry log-info">[INFO] DTF Gang Builder Professional Test Suite Initialized</div>
            <div class="log-entry log-success">[SUCCESS] Core CSS and JavaScript loaded</div>
            <div class="log-entry log-info">[INFO] Ready for testing...</div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <script src="assets/js/main.js"></script>
    
    <script>
        // Test Functions
        function logMessage(message, type = 'info') {
            const log = document.getElementById('test-log');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${type.toUpperCase()}] ${new Date().toLocaleTimeString()} - ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        function runUploadTest() {
            logMessage('Starting upload functionality test...', 'info');
            
            // Test upload zone
            const uploadZone = document.getElementById('upload-zone');
            if (uploadZone) {
                logMessage('Upload zone found and accessible', 'success');
            } else {
                logMessage('Upload zone not found', 'error');
            }
            
            // Test file input
            const fileInput = document.getElementById('file-input');
            if (fileInput) {
                logMessage('File input element found', 'success');
            } else {
                logMessage('File input element not found', 'error');
            }
            
            logMessage('Upload test completed', 'info');
        }

        function runCanvasTest() {
            logMessage('Starting canvas functionality test...', 'info');
            
            if (window.dtfBuilder && window.dtfBuilder.canvas) {
                logMessage('Canvas initialized successfully', 'success');
                logMessage(`Canvas size: ${window.dtfBuilder.canvas.width}x${window.dtfBuilder.canvas.height}`, 'info');
            } else {
                logMessage('Canvas not initialized', 'error');
            }
            
            logMessage('Canvas test completed', 'info');
        }

        function runExportTest() {
            logMessage('Starting export functionality test...', 'info');
            
            // Test PDF generation endpoint
            fetch('api/generate-pdf.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ test: true })
            })
            .then(response => {
                if (response.ok) {
                    logMessage('PDF generation endpoint accessible', 'success');
                } else {
                    logMessage('PDF generation endpoint returned error', 'warning');
                }
            })
            .catch(error => {
                logMessage('PDF generation endpoint not accessible', 'error');
            });
            
            logMessage('Export test completed', 'info');
        }

        function runPerformanceTest() {
            logMessage('Starting performance test...', 'info');
            
            const startTime = performance.now();
            
            // Test canvas rendering performance
            if (window.dtfBuilder && window.dtfBuilder.canvas) {
                window.dtfBuilder.canvas.renderAll();
                const renderTime = performance.now() - startTime;
                logMessage(`Canvas render time: ${renderTime.toFixed(2)}ms`, 'info');
                
                if (renderTime < 50) {
                    logMessage('Canvas performance: Excellent', 'success');
                } else if (renderTime < 100) {
                    logMessage('Canvas performance: Good', 'info');
                } else {
                    logMessage('Canvas performance: Needs optimization', 'warning');
                }
            }
            
            logMessage('Performance test completed', 'info');
        }

        function clearTestLog() {
            const log = document.getElementById('test-log');
            log.innerHTML = '<div class="log-entry log-info">[INFO] Test log cleared</div>';
        }

        // Initialize DTF Builder when page loads
        document.addEventListener('DOMContentLoaded', function() {
            logMessage('Initializing DTF Gang Builder...', 'info');
            
            if (typeof DTFGangBuilder !== 'undefined') {
                window.dtfBuilder = new DTFGangBuilder();
                logMessage('DTF Gang Builder initialized successfully', 'success');
            } else {
                logMessage('DTF Gang Builder class not found', 'error');
            }
        });
    </script>
</body>
</html>
