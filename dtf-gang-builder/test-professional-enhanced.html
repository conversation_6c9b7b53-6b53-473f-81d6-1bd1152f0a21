<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DTF Gang Builder - Professional</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">

</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <h1><i class="fas fa-layer-group"></i> DTF Gang Builder</h1>
            <p class="subtitle">Create Professional DTF Gang Sheets with Ease</p>
        </div>
    </header>

    <div class="container">

        <!-- Main DTF Gang Builder Interface -->
        <div class="main-container">
            <!-- Sidebar -->
            <aside class="sidebar">
                <!-- Upload Panel -->
                <div class="upload-panel">
                    <h3><i class="fas fa-upload"></i> Upload Images</h3>
                    
                    <div id="upload-zone" class="upload-zone">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="upload-text">
                            Drag & drop images here
                        </div>
                        <div class="upload-subtext">
                            or click to browse files
                        </div>
                        <input type="file" id="file-input" class="file-input" multiple accept="image/*">
                    </div>
                    
                    <button id="upload-button" class="upload-button">
                        <i class="fas fa-folder-open"></i> Choose Files
                    </button>
                    
                    <!-- Upload Progress -->
                    <div id="upload-progress" class="upload-progress">
                        <div class="progress-bar">
                            <div id="progress-fill" class="progress-fill"></div>
                        </div>
                        <div id="progress-text" class="progress-text">Uploading...</div>
                    </div>
                    
                    <!-- File List -->
                    <div id="file-list" class="file-list"></div>
                </div>

                <!-- Sheet Size Controls -->
                <div class="sheet-controls">
                    <h3><i class="fas fa-ruler-combined"></i> Sheet Size</h3>
                    
                    <select id="sheet-size" class="size-selector">
                        <option value="30x12">30x12" (30" × 12")</option>
                        <option value="30x24">30x24" (30" × 24")</option>
                        <option value="30x36">30x36" (30" × 36")</option>
                        <option value="30x48">30x48" (30" × 48")</option>
                        <option value="30x60">30x60" (30" × 60")</option>
                        <option value="30x72" selected>30x72" (30" × 72")</option>
                        <option value="30x100">30x100" (30" × 100")</option>
                        <option value="30x120">30x120" (30" × 120")</option>
                    </select>
                    
                    <div id="size-info" class="size-info">
                        Selected: 30" × 72" (Standard DTF Sheet)
                    </div>
                </div>

                <!-- Design Tools -->
                <div class="design-tools">
                    <h3><i class="fas fa-tools"></i> Tools</h3>

                    <div class="tool-group">
                        <button id="auto-arrange" class="tool-button">
                            <i class="fas fa-magic"></i> Auto Arrange
                        </button>
                        <button id="optimize-layout" class="tool-button">
                            <i class="fas fa-compress-arrows-alt"></i> Optimize
                        </button>
                        <button id="toggle-grid" class="tool-button">
                            <i class="fas fa-th"></i> Hide Grid
                        </button>
                    </div>

                    <div class="tool-group">
                        <button id="undo-btn" class="tool-button" disabled title="Undo">
                            <i class="fas fa-undo"></i> Undo
                        </button>
                        <button id="redo-btn" class="tool-button" disabled title="Redo">
                            <i class="fas fa-redo"></i> Redo
                        </button>
                    </div>

                    <div class="tool-group">
                        <button id="duplicate-selected" class="tool-button" disabled>
                            <i class="fas fa-copy"></i> Duplicate
                        </button>
                        <button id="delete-selected" class="tool-button" disabled>
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="main-content">
                <!-- Canvas Container -->
                <div class="canvas-container">
                    <!-- Canvas Toolbar -->
                    <div class="canvas-toolbar">
                        <div class="canvas-info">
                            <h3>Gang Sheet Canvas (30" × 72")</h3>
                            <span class="sheet-info">Professional DTF Gang Sheet Builder</span>
                        </div>

                        <div class="canvas-tools">
                            <button id="zoom-in" class="tool-button" title="Zoom In">
                                <i class="fas fa-search-plus"></i>
                            </button>
                            <button id="zoom-out" class="tool-button" title="Zoom Out">
                                <i class="fas fa-search-minus"></i>
                            </button>
                            <button id="zoom-fit" class="tool-button" title="Fit to View">
                                <i class="fas fa-expand-arrows-alt"></i>
                            </button>
                        </div>

                        <div class="zoom-controls">
                            <span>Zoom:</span>
                            <span id="zoom-level" class="zoom-level">100%</span>
                        </div>
                    </div>

                    <!-- Design Canvas -->
                    <canvas id="design-canvas"></canvas>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <button id="save-project" class="btn btn-secondary">
                        <i class="fas fa-save"></i> Save Project
                    </button>
                    <button id="generate-pdf" class="btn btn-primary">
                        <i class="fas fa-file-pdf"></i> Generate PDF
                    </button>
                    <button id="clear-canvas" class="btn btn-secondary">
                        <i class="fas fa-trash"></i> Clear Canvas
                    </button>
                </div>
            </main>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 DTF Gang Builder. All rights reserved.</p>
            <p>Supported formats: PNG, JPG, JPEG, GIF, SVG, WEBP | Max file size: 10MB</p>
        </div>
    </footer>

    <!-- JavaScript Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <script src="assets/js/main.js"></script>
    
    <script>
        // Initialize DTF Builder when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Initializing DTF Gang Builder...');

            if (typeof DTFGangBuilder !== 'undefined') {
                window.dtfBuilder = new DTFGangBuilder();
                console.log('DTF Gang Builder initialized successfully');
            } else {
                console.error('DTF Gang Builder class not found');
            }
        });
    </script>
</body>
</html>
