<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DTF Gang Builder - Professional</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        /* Professional Accordion Styles */
        .main-grid {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 15px;
            height: calc(100vh - 120px);
            overflow: hidden;
        }

        .sidebar {
            background: white;
            border-radius: 10px;
            padding: 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            height: fit-content;
            overflow: hidden;
        }

        .accordion-item {
            border-bottom: 1px solid #eee;
        }

        .accordion-item:last-child {
            border-bottom: none;
        }

        .accordion-header {
            padding: 15px 20px;
            background: #f8f9fa;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 600;
            color: #2c3e50;
            transition: all 0.3s ease;
            border: none;
            width: 100%;
            text-align: left;
        }

        .accordion-header:hover {
            background: #e9ecef;
        }

        .accordion-header.active {
            background: #3498db;
            color: white;
        }

        .accordion-icon {
            transition: transform 0.3s ease;
            font-size: 0.8rem;
        }

        .accordion-header.active .accordion-icon {
            transform: rotate(180deg);
        }

        .accordion-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: white;
        }

        .accordion-content.active {
            max-height: 1000px;
        }

        .accordion-body {
            padding: 20px;
        }

        .control-section {
            margin-bottom: 20px;
        }

        .section-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: #2c3e50;
            font-size: 1.1rem;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-label {
            display: block;
            font-weight: 500;
            margin-bottom: 5px;
            color: #555;
            font-size: 0.9rem;
        }

        .control-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 0.9rem;
        }

        .control-input:focus {
            outline: none;
            border-color: #3498db;
        }

        .settings-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            margin-right: 8px;
        }

        .upload-zone {
            border: 2px dashed #bdc3c7;
            border-radius: 8px;
            padding: 30px 15px;
            text-align: center;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .upload-zone:hover {
            border-color: #3498db;
            background: #ecf0f1;
        }

        .upload-zone.has-files {
            border-color: #27ae60;
            background: #d5f4e6;
        }

        .upload-icon {
            font-size: 2rem;
            color: #3498db;
            margin-bottom: 10px;
        }

        .upload-text {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .upload-subtext {
            font-size: 0.8rem;
            color: #666;
        }

        .upload-progress {
            margin: 15px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 5px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            border-radius: 10px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .progress-text {
            font-size: 0.85rem;
            color: #666;
            text-align: center;
        }

        .file-list {
            margin-top: 15px;
        }

        .file-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid #eee;
            border-radius: 5px;
            margin-bottom: 10px;
            background: white;
        }

        .file-thumbnail {
            width: 40px;
            height: 40px;
            object-fit: cover;
            border-radius: 3px;
            margin-right: 10px;
        }

        .file-info {
            flex: 1;
        }

        .file-name {
            font-weight: 500;
            color: #2c3e50;
            font-size: 0.9rem;
        }

        .file-size {
            color: #7f8c8d;
            font-size: 0.8rem;
        }

        .btn-remove {
            padding: 5px 10px;
            background: #e74c3c;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8rem;
        }

        .btn-remove:hover {
            background: #c0392b;
        }

        .image-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #eee;
            border-radius: 5px;
            padding: 10px;
        }

        .image-item {
            display: flex;
            align-items: center;
            padding: 8px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 0.85rem;
        }

        .image-item:last-child {
            border-bottom: none;
        }

        .tool-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 20px;
        }

        .tool-button {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tool-button:hover {
            background: #f8f9fa;
            border-color: #3498db;
        }

        .tool-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .btn {
            width: 100%;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .canvas-area {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .canvas-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .canvas-info-header h3 {
            margin: 0;
            font-size: 1.1rem;
            color: #2c3e50;
        }

        .sheet-info {
            font-size: 0.85rem;
            color: #7f8c8d;
        }

        .canvas-tools {
            display: flex;
            gap: 8px;
        }

        .canvas-tools .tool-button {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            background: white;
            color: #495057;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            width: auto;
        }

        .canvas-tools .tool-button:hover {
            background: #e9ecef;
            border-color: #3498db;
            color: #3498db;
        }

        .canvas-tools .tool-button.active {
            background: #27ae60;
            color: white;
            border-color: #27ae60;
            box-shadow: 0 2px 8px rgba(39, 174, 96, 0.3);
        }

        .canvas-tools .tool-button.active:hover {
            background: #229954;
            border-color: #229954;
        }

        .tool-separator {
            width: 1px;
            height: 24px;
            background: #dee2e6;
            margin: 0 8px;
        }

        .zoom-controls {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            color: #666;
        }

        .zoom-level {
            font-weight: 600;
            color: #2c3e50;
            min-width: 50px;
            text-align: center;
        }

        .canvas-container {
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
            position: relative;
            overflow: hidden;
            margin-bottom: 15px;
            user-select: none;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 400px;
        }

        #design-canvas {
            display: block;
            max-width: 100%;
            background: white;
        }

        .canvas-info {
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
        }

        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #27ae60;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.75rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 15px 20px;
            margin-bottom: 15px;
            text-align: center;
            position: relative;
            z-index: 10;
        }

        .header h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
        }

        .subtitle {
            margin: 5px 0 0 0;
            font-size: 1rem;
            opacity: 0.9;
        }

        .container {
            max-width: 100vw;
            margin: 0;
            padding: 10px;
            min-height: 100vh;
        }

        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 30px;
        }

        .footer p {
            margin: 5px 0;
            font-size: 0.9rem;
        }

        .hidden { display: none; }
    </style>

</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1>🏭 Professional DTF Gang Sheet Builder</h1>
        <p class="subtitle">Industry-standard features for professional DTF printing</p>
    </div>

    <div class="container">

        <!-- Main DTF Gang Builder Interface -->
        <div class="main-grid">
            <!-- Professional Accordion Sidebar -->
            <div class="sidebar">
                <!-- File Management Accordion -->
                <div class="accordion-item">
                    <button class="accordion-header active" onclick="toggleAccordion(this)">
                        <span>📁 File Management</span>
                        <span class="accordion-icon">▼</span>
                    </button>
                    <div class="accordion-content active">
                        <div class="accordion-body">
                            <div id="upload-zone" class="upload-zone">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <div class="upload-text">
                                    📤 Drop files or click to upload
                                </div>
                                <div class="upload-subtext">
                                    PNG, JPG, PDF, AI, EPS • Max 50MB each
                                </div>
                            </div>
                            <input type="file" id="file-input" multiple accept="image/*,.pdf,.ai,.eps" style="display: none;">

                            <button id="upload-button" class="btn btn-primary">
                                <i class="fas fa-folder-open"></i> Choose Files
                            </button>

                            <!-- Upload Progress -->
                            <div id="upload-progress" class="upload-progress hidden">
                                <div class="progress-bar">
                                    <div id="progress-fill" class="progress-fill"></div>
                                </div>
                                <div id="progress-text" class="progress-text">Uploading...</div>
                            </div>

                            <!-- File List -->
                            <div id="file-list" class="file-list hidden"></div>
                        </div>
                    </div>
                </div>

                <!-- Sheet Configuration Accordion -->
                <div class="accordion-item">
                    <button class="accordion-header" onclick="toggleAccordion(this)">
                        <span>📐 Sheet Configuration</span>
                        <span class="accordion-icon">▼</span>
                    </button>
                    <div class="accordion-content">
                        <div class="accordion-body">
                            <div class="control-group">
                                <label class="control-label">Sheet Size</label>
                                <select id="sheet-size" class="control-input">
                                    <option value="30x12">30" × 12" (Standard)</option>
                                    <option value="30x24">30" × 24"</option>
                                    <option value="30x36">30" × 36"</option>
                                    <option value="30x48">30" × 48"</option>
                                    <option value="30x60">30" × 60"</option>
                                    <option value="30x72" selected>30" × 72" (Popular)</option>
                                    <option value="30x100">30" × 100"</option>
                                    <option value="30x120">30" × 120" (Max)</option>
                                </select>
                            </div>

                            <div class="settings-grid">
                                <div class="control-group">
                                    <label class="control-label">DPI</label>
                                    <select id="dpi" class="control-input">
                                        <option value="150">150 DPI</option>
                                        <option value="300" selected>300 DPI</option>
                                        <option value="600">600 DPI</option>
                                    </select>
                                </div>
                                <div class="control-group">
                                    <label class="control-label">Color Mode</label>
                                    <select id="color-mode" class="control-input">
                                        <option value="cmyk" selected>CMYK</option>
                                        <option value="rgb">RGB</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Layout Tools Accordion -->
                <div class="accordion-item">
                    <button class="accordion-header" onclick="toggleAccordion(this)">
                        <span>🎯 Layout Tools</span>
                        <span class="accordion-icon">▼</span>
                    </button>
                    <div class="accordion-content">
                        <div class="accordion-body">
                            <div class="control-section">
                                <div class="section-title">🧩 Auto-Nesting</div>
                                <div class="settings-grid">
                                    <div class="control-group">
                                        <label class="control-label">Spacing (inches)</label>
                                        <input type="number" id="spacing" class="control-input" value="0.125" min="0" max="2" step="0.125">
                                    </div>
                                    <div class="control-group">
                                        <label class="control-label">Bleed (inches)</label>
                                        <input type="number" id="bleed" class="control-input" value="0.0625" min="0" max="0.5" step="0.0625">
                                    </div>
                                </div>

                                <div class="control-group">
                                    <label class="control-label">Nesting Algorithm</label>
                                    <select id="nesting-algorithm" class="control-input">
                                        <option value="efficiency" selected>Maximum Efficiency</option>
                                        <option value="speed">Fastest Processing</option>
                                        <option value="uniform">Uniform Spacing</option>
                                        <option value="rows">Row-by-Row</option>
                                    </select>
                                </div>

                                <div class="checkbox-group">
                                    <input type="checkbox" id="auto-rotate" checked>
                                    <label for="auto-rotate">Auto-rotate for optimal fit</label>
                                </div>

                                <div class="checkbox-group">
                                    <input type="checkbox" id="maintain-aspect" checked>
                                    <label for="maintain-aspect">Maintain aspect ratio</label>
                                </div>

                                <button id="auto-arrange" class="btn btn-primary">
                                    <i class="fas fa-magic"></i> Auto Arrange
                                </button>
                                <button id="fill-sheet-btn" class="btn btn-success">
                                    <i class="fas fa-th"></i> Fill Entire Sheet
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tools Accordion -->
                <div class="accordion-item">
                    <button class="accordion-header" onclick="toggleAccordion(this)">
                        <span>🛠️ Tools</span>
                        <span class="accordion-icon">▼</span>
                    </button>
                    <div class="accordion-content">
                        <div class="accordion-body">
                            <div class="tool-group">
                                <button id="undo-btn" class="tool-button" disabled title="Undo">
                                    <i class="fas fa-undo"></i> Undo
                                </button>
                                <button id="redo-btn" class="tool-button" disabled title="Redo">
                                    <i class="fas fa-redo"></i> Redo
                                </button>
                            </div>

                            <div class="tool-group">
                                <button id="duplicate-selected" class="tool-button" disabled>
                                    <i class="fas fa-copy"></i> Duplicate
                                </button>
                                <button id="delete-selected" class="tool-button" disabled>
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </div>

                            <div class="tool-group">
                                <button id="toggle-grid" class="tool-button">
                                    <i class="fas fa-th"></i> Toggle Grid
                                </button>
                                <button id="clear-canvas" class="tool-button">
                                    <i class="fas fa-eraser"></i> Clear All
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Export Accordion -->
                <div class="accordion-item">
                    <button class="accordion-header" onclick="toggleAccordion(this)">
                        <span>📤 Export & Save</span>
                        <span class="accordion-icon">▼</span>
                    </button>
                    <div class="accordion-content">
                        <div class="accordion-body">
                            <button id="save-project" class="btn btn-secondary">
                                <i class="fas fa-save"></i> Save Project
                            </button>
                            <button id="generate-pdf" class="btn btn-primary">
                                <i class="fas fa-file-pdf"></i> Generate PDF
                            </button>
                            <button id="export-png" class="btn btn-secondary">
                                <i class="fas fa-image"></i> Export PNG
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Canvas Area -->
            <div class="canvas-area">
                <!-- Canvas Toolbar -->
                <div class="canvas-toolbar">
                    <div class="canvas-info-header">
                        <h3>Gang Sheet Canvas (30" × 72")</h3>
                        <span class="sheet-info">Professional DTF Gang Sheet Builder</span>
                    </div>

                    <div class="canvas-tools">
                        <button id="zoom-in" class="tool-button" title="Zoom In">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <button id="zoom-out" class="tool-button" title="Zoom Out">
                            <i class="fas fa-search-minus"></i>
                        </button>
                        <button id="zoom-fit" class="tool-button" title="Fit to View">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                        <div class="tool-separator"></div>
                        <button id="hand-tool" class="tool-button" title="Hand Tool">
                            <i class="fas fa-hand-paper"></i>
                        </button>
                        <button id="select-tool" class="tool-button active" title="Select Tool">
                            <i class="fas fa-mouse-pointer"></i>
                        </button>
                    </div>

                    <div class="zoom-controls">
                        <span>Zoom:</span>
                        <span id="zoom-level" class="zoom-level">100%</span>
                    </div>
                </div>

                <!-- Canvas Container -->
                <div class="canvas-container">
                    <canvas id="design-canvas"></canvas>
                </div>

                <!-- Canvas Info -->
                <div class="canvas-info">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value" id="images-count">0</div>
                            <div class="stat-label">Images</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="efficiency-percent">0%</div>
                            <div class="stat-label">Efficiency</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="estimated-price">$0.00</div>
                            <div class="stat-label">Est. Price</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 DTF Gang Builder. All rights reserved.</p>
            <p>Supported formats: PNG, JPG, JPEG, GIF, SVG, WEBP | Max file size: 10MB</p>
        </div>
    </footer>

    <!-- JavaScript Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <script src="assets/js/main.js"></script>
    
    <script>
        // Accordion functionality
        function toggleAccordion(header) {
            const content = header.nextElementSibling;
            const icon = header.querySelector('.accordion-icon');

            // Close all other accordions
            document.querySelectorAll('.accordion-header').forEach(h => {
                if (h !== header) {
                    h.classList.remove('active');
                    h.nextElementSibling.classList.remove('active');
                }
            });

            // Toggle current accordion
            header.classList.toggle('active');
            content.classList.toggle('active');
        }

        // Initialize DTF Builder when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Initializing DTF Gang Builder...');

            // Initialize accordion - keep first one open
            const firstAccordion = document.querySelector('.accordion-header');
            if (firstAccordion) {
                firstAccordion.classList.add('active');
                firstAccordion.nextElementSibling.classList.add('active');
            }

            if (typeof DTFGangBuilder !== 'undefined') {
                window.dtfBuilder = new DTFGangBuilder();
                console.log('DTF Gang Builder initialized successfully');

                // Set up additional event listeners for professional features
                setupProfessionalFeatures();

                // Initialize grid
                setTimeout(() => {
                    if (window.dtfBuilder && window.dtfBuilder.canvas) {
                        window.dtfBuilder.drawGrid();
                    }
                }, 500);
            } else {
                console.error('DTF Gang Builder class not found');

                // Fallback: Set up basic upload functionality
                setupBasicUpload();
            }
        });

        // Basic upload functionality fallback
        function setupBasicUpload() {
            console.log('Setting up basic upload functionality...');

            const uploadZone = document.getElementById('upload-zone');
            const fileInput = document.getElementById('file-input');
            const uploadButton = document.getElementById('upload-button');

            if (uploadZone && fileInput) {
                uploadZone.addEventListener('click', () => {
                    console.log('Upload zone clicked');
                    fileInput.click();
                });

                fileInput.addEventListener('change', (e) => {
                    console.log('Files selected:', e.target.files.length);
                    if (e.target.files.length > 0) {
                        alert(`${e.target.files.length} file(s) selected. Upload functionality will be available when DTF Gang Builder is fully loaded.`);
                    }
                });
            }

            if (uploadButton) {
                uploadButton.addEventListener('click', () => {
                    console.log('Upload button clicked');
                    fileInput.click();
                });
            }
        }

        function setupProfessionalFeatures() {
            // Fill Sheet button
            const fillSheetBtn = document.getElementById('fill-sheet-btn');
            if (fillSheetBtn) {
                fillSheetBtn.addEventListener('click', function() {
                    if (window.dtfBuilder && typeof window.dtfBuilder.fillEntireSheet === 'function') {
                        window.dtfBuilder.fillEntireSheet();
                    } else {
                        console.log('Fill sheet functionality not yet implemented');
                        alert('Fill sheet feature coming soon!');
                    }
                });
            }

            // Grid toggle button
            const toggleGridBtn = document.getElementById('toggle-grid');
            if (toggleGridBtn) {
                toggleGridBtn.addEventListener('click', function() {
                    if (window.dtfBuilder) {
                        window.dtfBuilder.toggleGrid();
                    }
                });
            }

            // Export PNG button
            const exportPngBtn = document.getElementById('export-png');
            if (exportPngBtn) {
                exportPngBtn.addEventListener('click', function() {
                    if (window.dtfBuilder && typeof window.dtfBuilder.exportPNG === 'function') {
                        window.dtfBuilder.exportPNG();
                    } else {
                        console.log('PNG export functionality not yet implemented');
                        alert('PNG export feature coming soon!');
                    }
                });
            }

            // Hand tool and select tool
            const handTool = document.getElementById('hand-tool');
            const selectTool = document.getElementById('select-tool');

            if (handTool) {
                handTool.addEventListener('click', function() {
                    selectTool.classList.remove('active');
                    handTool.classList.add('active');
                    // Enable pan mode
                    if (window.dtfBuilder && window.dtfBuilder.canvas) {
                        window.dtfBuilder.canvas.selection = false;
                        window.dtfBuilder.canvas.defaultCursor = 'grab';
                    }
                });
            }

            if (selectTool) {
                selectTool.addEventListener('click', function() {
                    handTool.classList.remove('active');
                    selectTool.classList.add('active');
                    // Enable selection mode
                    if (window.dtfBuilder && window.dtfBuilder.canvas) {
                        window.dtfBuilder.canvas.selection = true;
                        window.dtfBuilder.canvas.defaultCursor = 'default';
                    }
                });
            }

            // Update stats periodically
            setInterval(updateStats, 1000);
        }

        function updateStats() {
            if (!window.dtfBuilder || !window.dtfBuilder.canvas) return;

            const imagesCount = window.dtfBuilder.uploadedImages ? window.dtfBuilder.uploadedImages.length : 0;
            const efficiency = calculateEfficiency();
            const estimatedPrice = calculatePrice();

            document.getElementById('images-count').textContent = imagesCount;
            document.getElementById('efficiency-percent').textContent = efficiency + '%';
            document.getElementById('estimated-price').textContent = '$' + estimatedPrice.toFixed(2);
        }

        function calculateEfficiency() {
            // Simple efficiency calculation - can be enhanced
            if (!window.dtfBuilder || !window.dtfBuilder.canvas) return 0;

            const objects = window.dtfBuilder.canvas.getObjects();
            if (objects.length === 0) return 0;

            let totalArea = 0;
            objects.forEach(obj => {
                totalArea += obj.getScaledWidth() * obj.getScaledHeight();
            });

            const canvasArea = window.dtfBuilder.canvas.width * window.dtfBuilder.canvas.height;
            return Math.round((totalArea / canvasArea) * 100);
        }

        function calculatePrice() {
            // Simple pricing calculation - can be enhanced
            if (!window.dtfBuilder || !window.dtfBuilder.canvas) return 0;

            const objects = window.dtfBuilder.canvas.getObjects();
            const basePrice = 15.00; // Base sheet price
            const perImagePrice = 2.50; // Price per image

            return basePrice + (objects.length * perImagePrice);
        }

        // Add grid functionality to DTFGangBuilder
        if (typeof DTFGangBuilder !== 'undefined') {
            DTFGangBuilder.prototype.toggleGrid = function() {
                this.isGridVisible = !this.isGridVisible;
                this.drawGrid();

                const toggleBtn = document.getElementById('toggle-grid');
                if (toggleBtn) {
                    toggleBtn.innerHTML = this.isGridVisible ?
                        '<i class="fas fa-th"></i> Hide Grid' :
                        '<i class="fas fa-th"></i> Show Grid';
                }
            };

            DTFGangBuilder.prototype.drawGrid = function() {
                if (!this.canvas) return;

                // Remove existing grid
                const objects = this.canvas.getObjects();
                objects.forEach(obj => {
                    if (obj.isGrid) {
                        this.canvas.remove(obj);
                    }
                });

                if (!this.isGridVisible) {
                    this.canvas.renderAll();
                    return;
                }

                const gridSize = 50; // Grid size in pixels (represents 1 inch at 50 DPI)
                const canvasWidth = this.canvas.width;
                const canvasHeight = this.canvas.height;

                // Create vertical lines
                for (let i = 0; i <= canvasWidth; i += gridSize) {
                    const line = new fabric.Line([i, 0, i, canvasHeight], {
                        stroke: '#e0e0e0',
                        strokeWidth: 1,
                        selectable: false,
                        evented: false,
                        isGrid: true
                    });
                    this.canvas.add(line);
                    this.canvas.sendToBack(line);
                }

                // Create horizontal lines
                for (let i = 0; i <= canvasHeight; i += gridSize) {
                    const line = new fabric.Line([0, i, canvasWidth, i], {
                        stroke: '#e0e0e0',
                        strokeWidth: 1,
                        selectable: false,
                        evented: false,
                        isGrid: true
                    });
                    this.canvas.add(line);
                    this.canvas.sendToBack(line);
                }

                // Add grid numbers
                for (let x = 0; x <= canvasWidth; x += gridSize) {
                    for (let y = 0; y <= canvasHeight; y += gridSize) {
                        if (x === 0 && y === 0) continue;

                        const inchX = Math.round(x / gridSize);
                        const inchY = Math.round(y / gridSize);

                        if (inchX % 5 === 0 || inchY % 5 === 0) { // Show numbers every 5 inches
                            const text = new fabric.Text(`${inchX},${inchY}`, {
                                left: x + 2,
                                top: y + 2,
                                fontSize: 10,
                                fill: '#999',
                                selectable: false,
                                evented: false,
                                isGrid: true
                            });
                            this.canvas.add(text);
                            this.canvas.sendToBack(text);
                        }
                    }
                }

                this.canvas.renderAll();
            };

            DTFGangBuilder.prototype.updateCanvasSize = function() {
                if (!this.canvas) return;

                // Set canvas size based on sheet dimensions
                const containerWidth = 800; // Max canvas width
                const containerHeight = 600; // Max canvas height

                // Default to 30x72 if no sheet size set
                const sheetWidth = this.sheetDimensions ? this.sheetDimensions.width : 30;
                const sheetHeight = this.sheetDimensions ? this.sheetDimensions.height : 72;

                // Calculate scale to fit container
                const scaleX = containerWidth / (sheetWidth * 50); // 50 pixels per inch
                const scaleY = containerHeight / (sheetHeight * 50);
                const scale = Math.min(scaleX, scaleY, 1); // Don't scale up

                const canvasWidth = sheetWidth * 50 * scale;
                const canvasHeight = sheetHeight * 50 * scale;

                this.canvas.setDimensions({
                    width: canvasWidth,
                    height: canvasHeight
                });

                // Update canvas info
                const canvasInfo = document.querySelector('.canvas-info-header h3');
                if (canvasInfo) {
                    canvasInfo.textContent = `Gang Sheet Canvas (${sheetWidth}" × ${sheetHeight}")`;
                }

                // Redraw grid
                this.drawGrid();
            };
        }
    </script>
</body>
</html>
