<?php
/**
 * DTF Gang Builder - Simple Upload API
 * 
 * Simple file upload without database dependencies for demo.
 */

// Define application constant
define('DTF_GANG_BUILDER', true);

// Include required files
require_once '../includes/config.php';
require_once '../includes/simple-demo.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

try {
    // Check if files were uploaded
    if (!isset($_FILES['files']) || empty($_FILES['files']['name'][0])) {
        throw new Exception('No files uploaded');
    }

    // Check for upload errors
    if ($_FILES['files']['error'][0] !== UPLOAD_ERR_OK) {
        $error_messages = [
            UPLOAD_ERR_INI_SIZE => 'File exceeds upload_max_filesize directive',
            UPLOAD_ERR_FORM_SIZE => 'File exceeds MAX_FILE_SIZE directive',
            UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
            UPLOAD_ERR_NO_FILE => 'No file was uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'File upload stopped by extension'
        ];
        $error_code = $_FILES['files']['error'][0];
        throw new Exception($error_messages[$error_code] ?? 'Unknown upload error');
    }

    // Create upload directory if it doesn't exist
    $upload_dir = DTF_BASE_PATH . 'uploads/';
    if (!is_dir($upload_dir)) {
        if (!mkdir($upload_dir, 0755, true)) {
            throw new Exception('Failed to create upload directory');
        }
    }

    // Verify upload directory is writable
    if (!is_writable($upload_dir)) {
        throw new Exception('Upload directory is not writable');
    }

    // Create data directory if it doesn't exist
    $data_dir = DTF_BASE_PATH . 'data/';
    if (!is_dir($data_dir)) {
        if (!mkdir($data_dir, 0755, true)) {
            throw new Exception('Failed to create data directory');
        }
    }
    
    $uploaded_files = [];
    $errors = [];
    
    // Process each uploaded file
    $file_count = count($_FILES['files']['name']);
    
    for ($i = 0; $i < $file_count; $i++) {
        $file_name = $_FILES['files']['name'][$i];
        $file_tmp = $_FILES['files']['tmp_name'][$i];
        $file_size = $_FILES['files']['size'][$i];
        $file_error = $_FILES['files']['error'][$i];
        
        // Skip if no file or error
        if ($file_error !== UPLOAD_ERR_OK || empty($file_name)) {
            continue;
        }
        
        try {
            // Validate file
            $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
            $file_type = mime_content_type($file_tmp);
            
            if (!in_array($file_type, $allowed_types)) {
                throw new Exception("File type not allowed: {$file_type}");
            }
            
            // Check file size (max 10MB)
            if ($file_size > 10 * 1024 * 1024) {
                throw new Exception("File too large: " . formatFileSize($file_size));
            }
            
            // Generate unique filename
            $file_extension = pathinfo($file_name, PATHINFO_EXTENSION);
            $unique_name = uniqid('dtf_') . '.' . $file_extension;
            $file_path = $upload_dir . $unique_name;
            
            // Move uploaded file
            if (!move_uploaded_file($file_tmp, $file_path)) {
                throw new Exception("Failed to move uploaded file");
            }
            
            // Get image dimensions
            $image_info = getimagesize($file_path);
            $width = $image_info[0] ?? 0;
            $height = $image_info[1] ?? 0;
            
            // Create file record with all fields expected by main.js
            $file_record = [
                'id' => uniqid(),
                'filename' => $unique_name, // For compatibility
                'original_filename' => $file_name,
                'file_path' => $file_path,
                'file_size' => $file_size,
                'mime_type' => $file_type,
                'width' => $width,
                'height' => $height,
                'created_at' => date('Y-m-d H:i:s'),
                'url' => str_replace(DTF_BASE_PATH, DTF_BASE_URL, $file_path),
                'thumbnail' => str_replace(DTF_BASE_PATH, DTF_BASE_URL, $file_path) // Use same URL as thumbnail for now
            ];
            
            // Save to JSON file
            $images_file = $data_dir . 'images.json';
            $existing_images = [];
            
            if (file_exists($images_file)) {
                $content = file_get_contents($images_file);
                $existing_images = json_decode($content, true) ?: [];
            }
            
            $existing_images[] = $file_record;
            file_put_contents($images_file, json_encode($existing_images, JSON_PRETTY_PRINT));
            
            $uploaded_files[] = $file_record;
            
        } catch (Exception $e) {
            $errors[] = [
                'file' => $file_name,
                'error' => $e->getMessage()
            ];
        }
    }
    
    // Prepare response - format expected by main.js
    $response = [
        'success' => true,
        'data' => $uploaded_files, // Main.js expects this to be the array directly
        'total_uploaded' => count($uploaded_files),
        'total_failed' => count($errors),
        'message' => generateUploadMessage(count($uploaded_files), count($errors))
    ];
    
    if (!empty($errors)) {
        $response['errors'] = $errors;
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Generate upload success message
 */
function generateUploadMessage($successful, $failed) {
    $message = '';
    
    if ($successful > 0) {
        $message .= $successful . ' file' . ($successful > 1 ? 's' : '') . ' uploaded successfully';
    }
    
    if ($failed > 0) {
        if ($successful > 0) {
            $message .= ', ';
        }
        $message .= $failed . ' file' . ($failed > 1 ? 's' : '') . ' failed';
    }
    
    if ($successful === 0 && $failed === 0) {
        $message = 'No files processed';
    }
    
    return $message;
}

/**
 * Format file size in human readable format
 */
function formatFileSize($bytes) {
    if ($bytes === 0) return '0 Bytes';
    
    $k = 1024;
    $sizes = ['Bytes', 'KB', 'MB', 'GB'];
    $i = floor(log($bytes) / log($k));
    
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}

?>
