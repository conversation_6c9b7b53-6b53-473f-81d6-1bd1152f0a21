/**
 * DTF Gang Builder - Main JavaScript File
 * 
 * This file contains the core JavaScript functionality for the DTF Gang Builder application.
 */

class DTFGangBuilder {
    constructor() {
        this.canvas = null;
        this.uploadedImages = [];
        this.currentSheetSize = '30x72';
        this.sheetDimensions = null;
        this.isGridVisible = true;
        this.currentZoom = 1;
        this.currentProjectId = null;
        this.revisionHistory = [];
        this.currentRevision = 0;
        this.undoStack = [];
        this.redoStack = [];
        this.maxUndoSteps = 20;
        this.autoSaveInterval = null;

        // Professional settings
        this.imageSpacing = 3; // mm
        this.bleedArea = 1; // mm
        this.nestingAlgorithm = 'efficiency';
        this.autoRotate = true;
        this.addCropMarks = false;
        this.safetyMargins = true;
        this.selectedObjects = [];

        this.init();
    }

    /**
     * Initialize the application
     */
    init() {
        this.setupEventListeners();
        this.initializeCanvas();
        this.loadSheetSize();
        
        console.log('DTF Gang Builder initialized');
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        console.log('Setting up event listeners...');

        // File upload events
        const uploadZone = document.getElementById('upload-zone');
        const fileInput = document.getElementById('file-input');
        const uploadButton = document.getElementById('upload-button');

        console.log('Upload elements found:', {
            uploadZone: !!uploadZone,
            fileInput: !!fileInput,
            uploadButton: !!uploadButton
        });

        if (uploadZone && fileInput) {
            console.log('Setting up drag and drop events...');
            // Drag and drop events
            uploadZone.addEventListener('dragover', this.handleDragOver.bind(this));
            uploadZone.addEventListener('dragleave', this.handleDragLeave.bind(this));
            uploadZone.addEventListener('drop', this.handleDrop.bind(this));

            // Click to upload
            uploadZone.addEventListener('click', () => {
                console.log('Upload zone clicked');
                fileInput.click();
            });
            fileInput.addEventListener('change', this.handleFileSelect.bind(this));

            console.log('Upload event listeners set up successfully');
        } else {
            console.error('Upload elements not found!');
        }

        if (uploadButton) {
            uploadButton.addEventListener('click', () => fileInput.click());
        }

        // Sheet size selector
        const sizeSelector = document.getElementById('sheet-size');
        if (sizeSelector) {
            sizeSelector.addEventListener('change', this.handleSheetSizeChange.bind(this));
        }

        // Canvas tools
        const zoomInBtn = document.getElementById('zoom-in');
        const zoomOutBtn = document.getElementById('zoom-out');
        const zoomFitBtn = document.getElementById('zoom-fit');
        const toggleGridBtn = document.getElementById('toggle-grid');

        if (zoomInBtn) zoomInBtn.addEventListener('click', () => this.zoomCanvas(1.2));
        if (zoomOutBtn) zoomOutBtn.addEventListener('click', () => this.zoomCanvas(0.8));
        if (zoomFitBtn) zoomFitBtn.addEventListener('click', () => this.fitCanvasToView());
        if (toggleGridBtn) toggleGridBtn.addEventListener('click', () => this.toggleGrid());

        // Action buttons
        const saveBtn = document.getElementById('save-project');
        const generateBtn = document.getElementById('generate-pdf');
        const clearBtn = document.getElementById('clear-canvas');

        if (saveBtn) saveBtn.addEventListener('click', () => this.saveProject());
        if (generateBtn) generateBtn.addEventListener('click', () => this.generatePDF());
        if (clearBtn) clearBtn.addEventListener('click', () => this.clearCanvas());
    }

    /**
     * Initialize Fabric.js canvas
     */
    initializeCanvas() {
        const canvasElement = document.getElementById('design-canvas');
        if (!canvasElement) {
            console.error('Canvas element not found');
            return;
        }

        // Initialize Fabric.js canvas
        this.canvas = new fabric.Canvas('design-canvas', {
            backgroundColor: '#ffffff',
            selection: true,
            preserveObjectStacking: true
        });

        // Set initial canvas size
        this.updateCanvasSize();

        // Canvas event listeners
        this.canvas.on('object:added', () => this.updateCanvasState());
        this.canvas.on('object:removed', () => this.updateCanvasState());
        this.canvas.on('object:modified', () => this.updateCanvasState());
        this.canvas.on('selection:created', () => {
            this.updateSelectionButtons();
            this.updateSelectionStatus();
        });
        this.canvas.on('selection:updated', () => {
            this.updateSelectionButtons();
            this.updateSelectionStatus();
        });
        this.canvas.on('selection:cleared', () => {
            this.updateSelectionButtons();
            this.updateSelectionStatus();
        });

        // Start auto-save
        this.startAutoSave();
    }

    /**
     * Handle drag over event
     */
    handleDragOver(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.add('dragover');
    }

    /**
     * Handle drag leave event
     */
    handleDragLeave(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.remove('dragover');
    }

    /**
     * Handle drop event
     */
    handleDrop(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.remove('dragover');

        console.log('Drop event triggered');
        const files = Array.from(e.dataTransfer.files);
        console.log('Files dropped:', files.length);

        if (files.length === 0) {
            this.showError('No files detected in drop');
            return;
        }

        this.handleFiles(files);
    }

    /**
     * Handle file select event
     */
    handleFileSelect(e) {
        const files = Array.from(e.target.files);
        this.handleFiles(files);
    }

    /**
     * Handle selected files
     */
    handleFiles(files) {
        console.log('handleFiles called with:', files.length, 'files');

        if (files.length === 0) {
            console.log('No files to handle');
            return;
        }

        // Validate files
        console.log('Validating files...');
        const validFiles = files.filter(file => {
            console.log('Validating file:', file.name, file.type, file.size);
            return this.validateFile(file);
        });

        console.log('Valid files:', validFiles.length);

        if (validFiles.length === 0) {
            this.showError('No valid image files selected');
            return;
        }

        // Upload files
        console.log('Starting upload for', validFiles.length, 'files');
        this.uploadFiles(validFiles);
    }

    /**
     * Validate file
     */
    validateFile(file) {
        const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp', 'image/svg+xml'];
        const maxSize = 100 * 1024 * 1024; // 100MB

        if (!allowedTypes.includes(file.type)) {
            this.showError(`File type not allowed: ${file.name}`);
            return false;
        }

        if (file.size > maxSize) {
            this.showError(`File too large: ${file.name}`);
            return false;
        }

        return true;
    }

    /**
     * Upload files to server
     */
    async uploadFiles(files) {
        console.log('uploadFiles called with:', files.length, 'files');

        const formData = new FormData();

        files.forEach((file, index) => {
            console.log(`Adding file ${index}:`, file.name);
            formData.append(`files[${index}]`, file);
        });

        // Add CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (csrfToken) {
            formData.append('csrf_token', csrfToken);
        }

        try {
            this.showProgress(0);
            console.log('Sending upload request to api/simple-upload.php');

            const response = await fetch('api/simple-upload.php', {
                method: 'POST',
                body: formData
            });

            console.log('Upload response status:', response.status);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const responseText = await response.text();
            console.log('Raw response:', responseText);

            let result;
            try {
                result = JSON.parse(responseText);
            } catch (parseError) {
                console.error('JSON parse error:', parseError);
                throw new Error('Invalid JSON response from server');
            }

            console.log('Parsed result:', result);

            if (result.success) {
                console.log('Upload successful, handling success...');
                this.handleUploadSuccess(result.data || result.uploadedFiles || []);
            } else {
                console.error('Upload failed:', result.error);
                this.showError(result.error || 'Upload failed');
            }
        } catch (error) {
            console.error('Upload error:', error);
            this.showError('Upload failed: ' + error.message);
        } finally {
            this.hideProgress();
        }
    }

    /**
     * Handle successful upload
     */
    handleUploadSuccess(uploadedFiles) {
        console.log('handleUploadSuccess called with:', uploadedFiles);

        if (!Array.isArray(uploadedFiles)) {
            console.error('uploadedFiles is not an array:', uploadedFiles);
            this.showError('Invalid upload response format');
            return;
        }

        if (uploadedFiles.length === 0) {
            console.warn('No files in upload response');
            this.showError('No files were uploaded');
            return;
        }

        let successCount = 0;
        let errorCount = 0;

        uploadedFiles.forEach((file, index) => {
            try {
                console.log(`Processing uploaded file ${index}:`, file);

                // Validate file data
                if (!file.url || !file.original_filename) {
                    throw new Error('Invalid file data structure');
                }

                this.uploadedImages.push(file);
                this.addImageToCanvas(file);
                this.addImageToList(file);
                successCount++;

            } catch (error) {
                console.error(`Error processing file ${index}:`, error);
                errorCount++;
            }
        });

        // Show appropriate feedback
        if (successCount > 0) {
            this.showSuccess(`${successCount} file(s) uploaded successfully`);
        }
        if (errorCount > 0) {
            this.showError(`${errorCount} file(s) failed to process`);
        }

        // Update UI state
        this.updateUploadZoneState();
    }

    /**
     * Add image to canvas
     */
    addImageToCanvas(imageData) {
        console.log('Adding image to canvas:', imageData.original_filename, imageData.url);

        fabric.Image.fromURL(imageData.url, (img) => {
            if (!img) {
                console.error('Failed to load image:', imageData.url);
                this.showError('Failed to load image: ' + imageData.original_filename);
                return;
            }

            console.log('Image loaded successfully:', imageData.original_filename);

            // Scale image to fit canvas if too large
            const maxWidth = this.canvas.width * 0.3;
            const maxHeight = this.canvas.height * 0.3;

            if (img.width > maxWidth || img.height > maxHeight) {
                const scale = Math.min(maxWidth / img.width, maxHeight / img.height);
                img.scale(scale);
                console.log('Image scaled by:', scale);
            }

            // Position image
            img.set({
                left: Math.random() * (this.canvas.width - img.getScaledWidth()),
                top: Math.random() * (this.canvas.height - img.getScaledHeight()),
                selectable: true,
                hasControls: true,
                hasBorders: true
            });

            // Add custom properties
            img.set('imageId', imageData.id);
            img.set('originalWidth', imageData.width);
            img.set('originalHeight', imageData.height);

            this.canvas.add(img);
            this.canvas.renderAll();

            // Update selection status
            this.updateSelectionStatus();

            console.log('Image added to canvas successfully');
            this.showSuccess('Image added: ' + imageData.original_filename);
        }, { crossOrigin: 'anonymous' });
    }

    /**
     * Add image to file list
     */
    addImageToList(imageData) {
        const fileList = document.getElementById('file-list');
        if (!fileList) return;

        const fileItem = document.createElement('div');
        fileItem.className = 'file-item';
        fileItem.innerHTML = `
            <img src="${imageData.thumbnail}" alt="${imageData.filename}" class="file-thumbnail">
            <div class="file-info">
                <div class="file-name">${imageData.original_filename}</div>
                <div class="file-size">${this.formatFileSize(imageData.file_size)}</div>
            </div>
            <div class="file-actions">
                <button class="btn-remove" onclick="dtfBuilder.removeImage(${imageData.id})">Remove</button>
            </div>
        `;

        fileList.appendChild(fileItem);
    }

    /**
     * Remove image
     */
    removeImage(imageId) {
        // Remove from canvas
        const objects = this.canvas.getObjects();
        objects.forEach(obj => {
            if (obj.imageId === imageId) {
                this.canvas.remove(obj);
            }
        });

        // Remove from uploaded images array
        this.uploadedImages = this.uploadedImages.filter(img => img.id !== imageId);

        // Remove from file list
        const fileList = document.getElementById('file-list');
        if (fileList) {
            const fileItems = fileList.querySelectorAll('.file-item');
            fileItems.forEach(item => {
                const removeBtn = item.querySelector('.btn-remove');
                if (removeBtn && removeBtn.onclick.toString().includes(imageId)) {
                    item.remove();
                }
            });
        }

        this.canvas.renderAll();
    }

    /**
     * Handle sheet size change
     */
    handleSheetSizeChange(e) {
        this.currentSheetSize = e.target.value;
        this.loadSheetSize();
        this.updateCanvasSize();
    }

    /**
     * Load sheet size information
     */
    async loadSheetSize() {
        try {
            const response = await fetch(`api/sheet-info.php?size=${this.currentSheetSize}`);
            const result = await response.json();

            if (result.success) {
                this.sheetDimensions = result.data;
                this.updateSheetInfo();
            }
        } catch (error) {
            console.error('Failed to load sheet size:', error);
        }
    }

    /**
     * Update canvas size based on sheet dimensions
     */
    updateCanvasSize() {
        if (!this.canvas || !this.sheetDimensions) return;

        const containerWidth = document.querySelector('.canvas-container').clientWidth - 40;
        const containerHeight = 600;

        // Calculate canvas size maintaining aspect ratio
        const aspectRatio = this.sheetDimensions.width_inches / this.sheetDimensions.height_inches;
        
        let canvasWidth = containerWidth;
        let canvasHeight = canvasWidth / aspectRatio;

        if (canvasHeight > containerHeight) {
            canvasHeight = containerHeight;
            canvasWidth = canvasHeight * aspectRatio;
        }

        this.canvas.setDimensions({
            width: canvasWidth,
            height: canvasHeight
        });

        this.drawGrid();
        this.canvas.renderAll();
    }

    /**
     * Update upload zone state
     */
    updateUploadZoneState() {
        const uploadZone = document.getElementById('upload-zone');
        const fileList = document.getElementById('file-list');

        if (uploadZone && this.uploadedImages.length > 0) {
            uploadZone.classList.add('has-files');
        }

        if (fileList && this.uploadedImages.length === 0) {
            fileList.innerHTML = '<div class="no-files-message">No files uploaded yet</div>';
        }
    }

    /**
     * Show success message
     */
    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    /**
     * Show error message
     */
    showError(message) {
        this.showNotification(message, 'error');
    }

    /**
     * Show progress indicator
     */
    showProgress(percentage) {
        const progressContainer = document.getElementById('upload-progress');
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');

        if (progressContainer) {
            progressContainer.style.display = 'block';
        }
        if (progressFill) {
            progressFill.style.width = percentage + '%';
        }
        if (progressText) {
            progressText.textContent = `Uploading... ${Math.round(percentage)}%`;
        }
    }

    /**
     * Hide progress indicator
     */
    hideProgress() {
        const progressContainer = document.getElementById('upload-progress');
        if (progressContainer) {
            progressContainer.style.display = 'none';
        }
    }

    /**
     * Show notification message
     */
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    /**
     * Get notification icon based on type
     */
    getNotificationIcon(type) {
        const icons = {
            'success': 'check-circle',
            'error': 'exclamation-circle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    /**
     * Format file size in human readable format
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Update selection buttons state
     */
    updateSelectionButtons() {
        const duplicateBtn = document.getElementById('duplicate-selected');
        const deleteBtn = document.getElementById('delete-selected');
        const hasSelection = this.canvas && this.canvas.getActiveObjects().length > 0;

        if (duplicateBtn) {
            duplicateBtn.disabled = !hasSelection;
        }
        if (deleteBtn) {
            deleteBtn.disabled = !hasSelection;
        }
    }

    /**
     * Update selection status display
     */
    updateSelectionStatus() {
        const statusElement = document.getElementById('selection-status');
        if (!statusElement || !this.canvas) return;

        const activeObjects = this.canvas.getActiveObjects();
        if (activeObjects.length === 0) {
            statusElement.innerHTML = '<i class="fas fa-info-circle"></i> Click on an image to select it first';
        } else if (activeObjects.length === 1) {
            statusElement.innerHTML = '<i class="fas fa-check-circle"></i> 1 image selected';
        } else {
            statusElement.innerHTML = `<i class="fas fa-check-circle"></i> ${activeObjects.length} images selected`;
        }
    }

    /**
     * Update canvas state for undo/redo
     */
    updateCanvasState() {
        if (!this.canvas) return;

        // Save current state for undo functionality
        const state = JSON.stringify(this.canvas.toJSON());
        this.undoStack.push(state);

        // Limit undo stack size
        if (this.undoStack.length > this.maxUndoSteps) {
            this.undoStack.shift();
        }

        // Clear redo stack when new action is performed
        this.redoStack = [];

        // Update undo/redo button states
        this.updateUndoRedoButtons();
    }

    /**
     * Update undo/redo button states
     */
    updateUndoRedoButtons() {
        const undoBtn = document.getElementById('undo-btn');
        const redoBtn = document.getElementById('redo-btn');

        if (undoBtn) {
            undoBtn.disabled = this.undoStack.length <= 1;
        }
        if (redoBtn) {
            redoBtn.disabled = this.redoStack.length === 0;
        }
    }

    /**
     * Start auto-save functionality
     */
    startAutoSave() {
        // Auto-save every 30 seconds
        this.autoSaveInterval = setInterval(() => {
            if (this.uploadedImages.length > 0) {
                this.autoSaveProject();
            }
        }, 30000);
    }

    /**
     * Auto-save project
     */
    autoSaveProject() {
        if (!this.canvas) return;

        try {
            const projectData = {
                images: this.uploadedImages,
                canvas: this.canvas.toJSON(),
                sheetSize: this.currentSheetSize,
                timestamp: new Date().toISOString()
            };

            localStorage.setItem('dtf_autosave', JSON.stringify(projectData));
            console.log('Project auto-saved');
        } catch (error) {
            console.error('Auto-save failed:', error);
        }
    }

    /**
     * Zoom canvas
     */
    zoomCanvas(factor) {
        if (!this.canvas) return;

        this.currentZoom *= factor;
        this.currentZoom = Math.max(0.1, Math.min(5, this.currentZoom)); // Limit zoom between 10% and 500%

        this.canvas.setZoom(this.currentZoom);
        this.updateZoomDisplay();
    }

    /**
     * Fit canvas to view
     */
    fitCanvasToView() {
        if (!this.canvas) return;

        const container = document.querySelector('.canvas-container');
        if (!container) return;

        const containerWidth = container.clientWidth - 40;
        const containerHeight = container.clientHeight - 100;

        const canvasWidth = this.canvas.width;
        const canvasHeight = this.canvas.height;

        const scaleX = containerWidth / canvasWidth;
        const scaleY = containerHeight / canvasHeight;
        const scale = Math.min(scaleX, scaleY);

        this.currentZoom = scale;
        this.canvas.setZoom(scale);
        this.updateZoomDisplay();
    }

    /**
     * Toggle grid visibility
     */
    toggleGrid() {
        this.isGridVisible = !this.isGridVisible;
        this.drawGrid();

        const toggleBtn = document.getElementById('toggle-grid');
        if (toggleBtn) {
            toggleBtn.textContent = this.isGridVisible ? 'Hide Grid' : 'Show Grid';
            toggleBtn.classList.toggle('active', this.isGridVisible);
        }
    }

    /**
     * Draw grid on canvas
     */
    drawGrid() {
        if (!this.canvas || !this.sheetDimensions) return;

        // Remove existing grid
        const existingGrid = this.canvas.getObjects().filter(obj => obj.isGrid);
        existingGrid.forEach(obj => this.canvas.remove(obj));

        if (!this.isGridVisible) {
            this.canvas.renderAll();
            return;
        }

        const gridSpacing = 50; // pixels
        const canvasWidth = this.canvas.width;
        const canvasHeight = this.canvas.height;

        // Vertical lines
        for (let x = 0; x <= canvasWidth; x += gridSpacing) {
            const line = new fabric.Line([x, 0, x, canvasHeight], {
                stroke: '#ddd',
                strokeWidth: 1,
                selectable: false,
                evented: false,
                isGrid: true
            });
            this.canvas.add(line);
        }

        // Horizontal lines
        for (let y = 0; y <= canvasHeight; y += gridSpacing) {
            const line = new fabric.Line([0, y, canvasWidth, y], {
                stroke: '#ddd',
                strokeWidth: 1,
                selectable: false,
                evented: false,
                isGrid: true
            });
            this.canvas.add(line);
        }

        this.canvas.renderAll();
    }

    /**
     * Update zoom display
     */
    updateZoomDisplay() {
        const zoomLevel = document.getElementById('zoom-level');
        if (zoomLevel) {
            zoomLevel.textContent = Math.round(this.currentZoom * 100) + '%';
        }
    }

    /**
     * Auto arrange images on canvas
     */
    autoArrangeImages() {
        if (!this.canvas || this.uploadedImages.length === 0) return;

        const objects = this.canvas.getObjects().filter(obj => !obj.isGrid);
        if (objects.length === 0) return;

        const spacing = 20;
        const margin = 50;
        let currentX = margin;
        let currentY = margin;
        let rowHeight = 0;

        objects.forEach(obj => {
            // Check if object fits in current row
            if (currentX + obj.getScaledWidth() > this.canvas.width - margin) {
                // Move to next row
                currentX = margin;
                currentY += rowHeight + spacing;
                rowHeight = 0;
            }

            // Position object
            obj.set({
                left: currentX,
                top: currentY
            });

            // Update position for next object
            currentX += obj.getScaledWidth() + spacing;
            rowHeight = Math.max(rowHeight, obj.getScaledHeight());
        });

        this.canvas.renderAll();
        this.showSuccess('Images arranged automatically');
    }

    /**
     * Clear canvas
     */
    clearCanvas() {
        if (!this.canvas) return;

        if (confirm('Are you sure you want to clear the canvas? This action cannot be undone.')) {
            this.canvas.clear();
            this.uploadedImages = [];
            this.updateUploadZoneState();
            this.drawGrid();
            this.showSuccess('Canvas cleared');
        }
    }

    /**
     * Save project
     */
    saveProject() {
        if (!this.canvas || this.uploadedImages.length === 0) {
            this.showError('No images to save');
            return;
        }

        // Open save modal
        const saveModal = document.getElementById('save-modal');
        if (saveModal) {
            saveModal.classList.add('show');
        }
    }

    /**
     * Generate PDF
     */
    async generatePDF() {
        if (!this.canvas || this.uploadedImages.length === 0) {
            this.showError('No images to export');
            return;
        }

        try {
            this.showProgress(0);

            // Get canvas data
            const canvasData = this.canvas.toJSON();

            // Prepare export data
            const exportData = {
                canvas: canvasData,
                sheetSize: this.currentSheetSize,
                images: this.uploadedImages,
                settings: {
                    dpi: 300,
                    format: 'pdf',
                    quality: 'high'
                }
            };

            this.showProgress(50);

            // Send to PDF generation API
            const response = await fetch('api/generate-pdf.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(exportData)
            });

            this.showProgress(75);

            if (!response.ok) {
                throw new Error('PDF generation failed');
            }

            const result = await response.json();

            if (result.success) {
                this.showProgress(100);

                // Download the PDF
                const link = document.createElement('a');
                link.href = result.download_url;
                link.download = result.filename;
                link.click();

                this.showSuccess('PDF generated successfully');
            } else {
                throw new Error(result.error || 'PDF generation failed');
            }

        } catch (error) {
            console.error('PDF generation error:', error);
            this.showError('Failed to generate PDF: ' + error.message);
        } finally {
            this.hideProgress();
        }
    }

    /**
     * Update sheet information display
     */
    updateSheetInfo() {
        const sizeInfo = document.getElementById('size-info');
        if (sizeInfo && this.sheetDimensions) {
            sizeInfo.innerHTML = `
                <strong>Dimensions:</strong> ${this.sheetDimensions.width_inches}" × ${this.sheetDimensions.height_inches}"<br>
                <strong>Pixels:</strong> ${this.sheetDimensions.width_pixels} × ${this.sheetDimensions.height_pixels}<br>
                <strong>DPI:</strong> ${this.sheetDimensions.dpi}
            `;
        }
    }

    /**
     * Draw grid on canvas
     */
    drawGrid() {
        if (!this.canvas || !this.isGridVisible) return;

        // Remove existing grid
        const objects = this.canvas.getObjects();
        objects.forEach(obj => {
            if (obj.isGrid) {
                this.canvas.remove(obj);
            }
        });

        const gridSpacing = 50; // pixels
        const canvasWidth = this.canvas.width;
        const canvasHeight = this.canvas.height;

        // Vertical lines
        for (let i = 0; i <= canvasWidth; i += gridSpacing) {
            const line = new fabric.Line([i, 0, i, canvasHeight], {
                stroke: '#e0e0e0',
                strokeWidth: 1,
                selectable: false,
                evented: false,
                isGrid: true
            });
            this.canvas.add(line);
            this.canvas.sendToBack(line);
        }

        // Horizontal lines
        for (let i = 0; i <= canvasHeight; i += gridSpacing) {
            const line = new fabric.Line([0, i, canvasWidth, i], {
                stroke: '#e0e0e0',
                strokeWidth: 1,
                selectable: false,
                evented: false,
                isGrid: true
            });
            this.canvas.add(line);
            this.canvas.sendToBack(line);
        }
    }

    /**
     * Toggle grid visibility
     */
    toggleGrid() {
        this.isGridVisible = !this.isGridVisible;
        
        if (this.isGridVisible) {
            this.drawGrid();
        } else {
            const objects = this.canvas.getObjects();
            objects.forEach(obj => {
                if (obj.isGrid) {
                    this.canvas.remove(obj);
                }
            });
        }
        
        this.canvas.renderAll();
        
        const toggleBtn = document.getElementById('toggle-grid');
        if (toggleBtn) {
            toggleBtn.textContent = this.isGridVisible ? 'Hide Grid' : 'Show Grid';
        }
    }

    /**
     * Zoom canvas
     */
    zoomCanvas(factor) {
        this.currentZoom *= factor;
        this.canvas.setZoom(this.currentZoom);
        this.updateZoomDisplay();
    }

    /**
     * Fit canvas to view
     */
    fitCanvasToView() {
        this.currentZoom = 1;
        this.canvas.setZoom(1);
        this.updateZoomDisplay();
    }

    /**
     * Update zoom display
     */
    updateZoomDisplay() {
        const zoomLevel = document.getElementById('zoom-level');
        if (zoomLevel) {
            zoomLevel.textContent = Math.round(this.currentZoom * 100) + '%';
        }
    }

    /**
     * Update canvas state
     */
    updateCanvasState() {
        // Save current state to undo stack
        this.saveToUndoStack();

        // Clear redo stack when new changes are made
        this.redoStack = [];

        // Update UI buttons
        this.updateHistoryButtons();

        console.log('Canvas state updated');
    }

    /**
     * Save project
     */
    async saveProject() {
        const projectData = {
            name: 'Untitled Project',
            sheet_size: this.currentSheetSize,
            canvas_data: JSON.stringify(this.canvas.toJSON()),
            images: this.uploadedImages
        };

        try {
            const response = await fetch('api/save-project.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(projectData)
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess('Project saved successfully');
            } else {
                this.showError(result.error || 'Failed to save project');
            }
        } catch (error) {
            console.error('Save error:', error);
            this.showError('Failed to save project');
        }
    }

    /**
     * Generate PDF
     */
    async generatePDF() {
        if (this.uploadedImages.length === 0) {
            this.showError('Please upload at least one image');
            return;
        }

        const generateData = {
            sheet_size: this.currentSheetSize,
            canvas_data: JSON.stringify(this.canvas.toJSON()),
            images: this.uploadedImages
        };

        try {
            this.showProgress(0, 'Generating PDF...');

            const response = await fetch('api/generate-pdf.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(generateData)
            });

            const result = await response.json();

            if (result.success) {
                // Download the generated PDF
                window.open(result.data.download_url, '_blank');
                this.showSuccess('PDF generated successfully');
            } else {
                this.showError(result.error || 'Failed to generate PDF');
            }
        } catch (error) {
            console.error('Generate error:', error);
            this.showError('Failed to generate PDF');
        } finally {
            this.hideProgress();
        }
    }

    /**
     * Clear canvas
     */
    clearCanvas() {
        if (confirm('Are you sure you want to clear the canvas? This action cannot be undone.')) {
            this.canvas.clear();
            this.canvas.backgroundColor = '#ffffff';
            this.uploadedImages = [];

            const fileList = document.getElementById('file-list');
            if (fileList) {
                fileList.innerHTML = '';
            }

            this.drawGrid();
            this.canvas.renderAll();
        }
    }

    /**
     * Auto arrange images with professional settings (client-side)
     */
    async autoArrangeImages() {
        if (this.uploadedImages.length === 0) {
            this.showError('Please upload some images first');
            return;
        }

        try {
            this.showProgress(0, 'Calculating optimal arrangement...');

            // Get all image objects on canvas (excluding grid)
            const imageObjects = this.canvas.getObjects().filter(obj => !obj.isGrid && !obj.isCropMark);

            if (imageObjects.length === 0) {
                this.showError('No images found on canvas');
                return;
            }

            // Calculate spacing and margins
            const spacingPixels = this.mmToPixels(this.imageSpacing);
            const marginPixels = this.mmToPixels(this.sheetMargins || 5);

            // Get canvas dimensions
            const canvasWidth = this.canvas.width - (marginPixels * 2);
            const canvasHeight = this.canvas.height - (marginPixels * 2);

            // Sort images by size (largest first for better packing)
            imageObjects.sort((a, b) => {
                const aArea = a.getScaledWidth() * a.getScaledHeight();
                const bArea = b.getScaledWidth() * b.getScaledHeight();
                return bArea - aArea;
            });

            // Arrange images in grid
            let currentX = marginPixels;
            let currentY = marginPixels;
            let rowHeight = 0;
            let arrangedCount = 0;

            imageObjects.forEach(obj => {
                const objWidth = obj.getScaledWidth();
                const objHeight = obj.getScaledHeight();

                // Check if object fits in current row
                if (currentX + objWidth > canvasWidth + marginPixels) {
                    // Move to next row
                    currentX = marginPixels;
                    currentY += rowHeight + spacingPixels;
                    rowHeight = 0;
                }

                // Check if object fits vertically
                if (currentY + objHeight <= canvasHeight + marginPixels) {
                    obj.set({
                        left: currentX,
                        top: currentY
                    });

                    currentX += objWidth + spacingPixels;
                    rowHeight = Math.max(rowHeight, objHeight);
                    arrangedCount++;
                }
            });

            this.canvas.renderAll();

            const efficiency = Math.round((arrangedCount / imageObjects.length) * 100);
            this.showSuccess(`Arranged ${arrangedCount} images with ${efficiency}% efficiency`);

        } catch (error) {
            console.error('Auto-arrange error:', error);
            this.showError('Failed to calculate arrangement');
        } finally {
            this.hideProgress();
        }
    }

    /**
     * Optimize layout for maximum efficiency
     */
    async optimizeLayout() {
        if (this.uploadedImages.length === 0) {
            this.showError('Please upload some images first');
            return;
        }

        // Temporarily enable all optimization settings
        const originalSettings = {
            autoRotate: this.autoRotate,
            algorithm: this.nestingAlgorithm
        };

        this.autoRotate = true;
        this.nestingAlgorithm = 'efficiency';

        try {
            await this.autoArrangeImages();
            this.showSuccess('Layout optimized for maximum efficiency!');
        } finally {
            // Restore original settings
            this.autoRotate = originalSettings.autoRotate;
            this.nestingAlgorithm = originalSettings.algorithm;
        }
    }

    /**
     * Apply arrangement to canvas
     */
    applyArrangement(arrangementData) {
        const objects = this.canvas.getObjects();

        // Clear existing images (keep grid)
        objects.forEach(obj => {
            if (!obj.isGrid) {
                this.canvas.remove(obj);
            }
        });

        // Apply new arrangement
        arrangementData.arranged.forEach(item => {
            const imageData = this.uploadedImages.find(img => img.id === item.image_id);
            if (imageData) {
                fabric.Image.fromURL(imageData.url, (img) => {
                    // Convert inches to pixels for canvas
                    const left = this.inchesToPixels(item.x);
                    const top = this.inchesToPixels(item.y);
                    const width = this.inchesToPixels(item.width);
                    const height = this.inchesToPixels(item.height);

                    img.set({
                        left: left,
                        top: top,
                        scaleX: width / img.width,
                        scaleY: height / img.height,
                        angle: item.rotation || 0,
                        selectable: true,
                        hasControls: true,
                        hasBorders: true
                    });

                    img.set('imageId', item.image_id);
                    img.set('originalWidth', imageData.width);
                    img.set('originalHeight', imageData.height);

                    this.canvas.add(img);
                });
            }
        });

        this.canvas.renderAll();

        // Show arrangement statistics
        if (arrangementData.not_arranged_count > 0) {
            this.showError(`${arrangementData.not_arranged_count} images could not fit on the sheet`);
        }
    }

    /**
     * Utility methods
     */
    formatFileSize(bytes) {
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        if (bytes === 0) return '0 Bytes';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }

    /**
     * Convert inches to pixels
     */
    inchesToPixels(inches, dpi = 300) {
        return inches * dpi;
    }

    /**
     * Convert pixels to inches
     */
    pixelsToInches(pixels, dpi = 300) {
        return pixels / dpi;
    }

    showProgress(percent, message = 'Uploading...') {
        const progressContainer = document.getElementById('upload-progress');
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');

        if (progressContainer) {
            progressContainer.style.display = 'block';
            if (progressFill) progressFill.style.width = percent + '%';
            if (progressText) progressText.textContent = message;
        }
    }

    hideProgress() {
        const progressContainer = document.getElementById('upload-progress');
        if (progressContainer) {
            progressContainer.style.display = 'none';
        }
    }

    showError(message) {
        alert('Error: ' + message);
        console.error(message);
    }

    showSuccess(message) {
        alert('Success: ' + message);
        console.log(message);
    }

    // =============================================================================
    // PROJECT HISTORY & UNDO/REDO FUNCTIONALITY
    // =============================================================================

    /**
     * Save current state to undo stack
     */
    saveToUndoStack() {
        if (!this.canvas) return;

        const state = {
            canvas: JSON.stringify(this.canvas.toJSON()),
            images: [...this.uploadedImages],
            sheetSize: this.currentSheetSize,
            timestamp: Date.now()
        };

        this.undoStack.push(state);

        // Limit undo stack size
        if (this.undoStack.length > this.maxUndoSteps) {
            this.undoStack.shift();
        }
    }

    /**
     * Undo last action
     */
    undo() {
        if (this.undoStack.length === 0) {
            this.showError('Nothing to undo');
            return;
        }

        // Save current state to redo stack
        const currentState = {
            canvas: JSON.stringify(this.canvas.toJSON()),
            images: [...this.uploadedImages],
            sheetSize: this.currentSheetSize,
            timestamp: Date.now()
        };
        this.redoStack.push(currentState);

        // Get previous state
        const previousState = this.undoStack.pop();
        this.restoreCanvasState(previousState);

        this.updateHistoryButtons();
        this.showSuccess('Undo completed');
    }

    /**
     * Redo last undone action
     */
    redo() {
        if (this.redoStack.length === 0) {
            this.showError('Nothing to redo');
            return;
        }

        // Save current state to undo stack
        this.saveToUndoStack();

        // Get next state
        const nextState = this.redoStack.pop();
        this.restoreCanvasState(nextState);

        this.updateHistoryButtons();
        this.showSuccess('Redo completed');
    }

    /**
     * Restore canvas state
     */
    restoreCanvasState(state) {
        try {
            // Clear canvas
            this.canvas.clear();
            this.canvas.backgroundColor = '#ffffff';

            // Restore canvas objects
            const canvasData = JSON.parse(state.canvas);
            this.canvas.loadFromJSON(canvasData, () => {
                this.canvas.renderAll();
                this.drawGrid();
            });

            // Restore other state
            this.uploadedImages = [...state.images];
            this.currentSheetSize = state.sheetSize;

            // Update UI
            const sizeSelector = document.getElementById('sheet-size');
            if (sizeSelector) {
                sizeSelector.value = this.currentSheetSize;
            }

            this.loadSheetSize();
            this.updateFileList();

        } catch (error) {
            console.error('Failed to restore canvas state:', error);
            this.showError('Failed to restore previous state');
        }
    }

    /**
     * Update file list display
     */
    updateFileList() {
        const fileList = document.getElementById('file-list');
        if (!fileList) return;

        fileList.innerHTML = '';
        this.uploadedImages.forEach(imageData => {
            this.addImageToList(imageData);
        });
    }

    /**
     * Update history buttons state
     */
    updateHistoryButtons() {
        const undoBtn = document.getElementById('undo-btn');
        const redoBtn = document.getElementById('redo-btn');

        if (undoBtn) {
            undoBtn.disabled = this.undoStack.length === 0;
            undoBtn.title = `Undo (${this.undoStack.length} actions available)`;
        }

        if (redoBtn) {
            redoBtn.disabled = this.redoStack.length === 0;
            redoBtn.title = `Redo (${this.redoStack.length} actions available)`;
        }
    }

    /**
     * Start auto-save functionality
     */
    startAutoSave() {
        // Auto-save every 30 seconds
        this.autoSaveInterval = setInterval(() => {
            if (this.currentProjectId && this.uploadedImages.length > 0) {
                this.autoSaveProject();
            }
        }, 30000);
    }

    /**
     * Auto-save project
     */
    async autoSaveProject() {
        if (!this.currentProjectId) return;

        try {
            await this.saveRevision('auto_save', 'Auto-save');
        } catch (error) {
            console.warn('Auto-save failed:', error);
        }
    }

    /**
     * Save project revision
     */
    async saveRevision(changeType = 'manual', description = '') {
        if (!this.currentProjectId) {
            this.showError('No active project to save revision');
            return;
        }

        const revisionData = {
            project_id: this.currentProjectId,
            canvas_data: JSON.stringify(this.canvas.toJSON()),
            images_data: JSON.stringify(this.uploadedImages),
            sheet_size: this.currentSheetSize,
            description: description,
            change_type: changeType
        };

        try {
            const response = await fetch('api/project-history.php?action=save', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(revisionData)
            });

            const result = await response.json();

            if (result.success) {
                this.currentRevision = result.data.revision_number;
                if (changeType === 'manual') {
                    this.showSuccess('Revision saved successfully');
                }
                return result.data;
            } else {
                throw new Error(result.error || 'Failed to save revision');
            }
        } catch (error) {
            console.error('Save revision error:', error);
            if (changeType === 'manual') {
                this.showError('Failed to save revision: ' + error.message);
            }
            throw error;
        }
    }

    /**
     * Load revision history
     */
    async loadRevisionHistory() {
        if (!this.currentProjectId) {
            this.showError('No active project');
            return;
        }

        try {
            const response = await fetch(`api/project-history.php?action=list&project_id=${this.currentProjectId}&limit=20`);
            const result = await response.json();

            if (result.success) {
                this.revisionHistory = result.data.revisions;
                this.displayRevisionHistory();
                return result.data;
            } else {
                throw new Error(result.error || 'Failed to load revision history');
            }
        } catch (error) {
            console.error('Load revision history error:', error);
            this.showError('Failed to load revision history: ' + error.message);
            throw error;
        }
    }

    /**
     * Display revision history
     */
    displayRevisionHistory() {
        const revisionList = document.getElementById('revision-list');
        const currentRevisionSpan = document.getElementById('current-revision-number');

        if (!revisionList) return;

        // Update current revision number
        if (currentRevisionSpan) {
            currentRevisionSpan.textContent = this.currentRevision || '-';
        }

        if (this.revisionHistory.length === 0) {
            revisionList.innerHTML = '<div class="loading-message">No revision history available</div>';
            return;
        }

        let html = '';
        this.revisionHistory.forEach(revision => {
            const isCurrent = revision.revision_number === this.currentRevision;
            const date = new Date(revision.created_at);
            const formattedDate = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();

            html += `
                <div class="revision-item ${isCurrent ? 'current' : ''}">
                    <div class="revision-info">
                        <div class="revision-number">
                            Revision #${revision.revision_number}
                            ${isCurrent ? '(Current)' : ''}
                        </div>
                        <div class="revision-description">
                            ${revision.change_description || 'No description'}
                        </div>
                        <div class="revision-meta">
                            ${formattedDate}
                            <span class="revision-type ${revision.change_type}">${revision.change_type}</span>
                        </div>
                    </div>
                    <div class="revision-actions">
                        <button class="btn-restore"
                                onclick="window.dtfBuilder.restoreToRevision(${revision.revision_number})"
                                ${isCurrent ? 'disabled' : ''}>
                            ${isCurrent ? 'Current' : 'Restore'}
                        </button>
                    </div>
                </div>
            `;
        });

        revisionList.innerHTML = html;
    }

    /**
     * Restore to specific revision
     */
    async restoreToRevision(revisionNumber) {
        if (!this.currentProjectId) {
            this.showError('No active project');
            return;
        }

        if (!confirm(`Are you sure you want to restore to revision #${revisionNumber}? This will create a new revision with the restored state.`)) {
            return;
        }

        try {
            this.showProgress(0, 'Restoring revision...');

            const response = await fetch('api/project-history.php?action=restore', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    project_id: this.currentProjectId,
                    revision_number: revisionNumber
                })
            });

            const result = await response.json();

            if (result.success) {
                // Restore the canvas state
                const restoredData = result.data.restored_revision;

                this.canvas.loadFromJSON(restoredData.canvas_data, () => {
                    this.canvas.renderAll();
                    this.drawGrid();
                });

                if (restoredData.images_data) {
                    this.uploadedImages = restoredData.images_data;
                    this.updateFileList();
                }

                this.currentSheetSize = restoredData.sheet_size;
                this.currentRevision = result.data.new_revision.revision_number;

                // Update UI
                const sizeSelector = document.getElementById('sheet-size');
                if (sizeSelector) {
                    sizeSelector.value = this.currentSheetSize;
                }

                this.loadSheetSize();
                this.showSuccess(`Restored to revision #${revisionNumber}`);
            } else {
                throw new Error(result.error || 'Failed to restore revision');
            }
        } catch (error) {
            console.error('Restore revision error:', error);
            this.showError('Failed to restore revision: ' + error.message);
        } finally {
            this.hideProgress();
        }
    }

    // =============================================================================
    // PROFESSIONAL OBJECT MANIPULATION
    // =============================================================================

    /**
     * Update selection buttons based on selected objects
     */
    updateSelectionButtons() {
        const activeSelection = this.canvas.getActiveObject();
        const hasSelection = activeSelection && !activeSelection.isGrid;

        const duplicateBtn = document.getElementById('duplicate-selected');
        const deleteBtn = document.getElementById('delete-selected');

        if (duplicateBtn) duplicateBtn.disabled = !hasSelection;
        if (deleteBtn) deleteBtn.disabled = !hasSelection;
    }

    /**
     * Update selection status indicator
     */
    updateSelectionStatus() {
        const statusElement = document.getElementById('selection-status');
        if (!statusElement) return;

        const activeObject = this.canvas.getActiveObject();
        const allObjects = this.canvas.getObjects();
        const imageObjects = allObjects.filter(obj => !obj.isGrid);

        // Remove all status classes
        statusElement.classList.remove('selected', 'error');

        if (imageObjects.length === 0) {
            statusElement.innerHTML = '<i class="fas fa-upload"></i> Upload an image first';
            statusElement.classList.add('error');
        } else if (!activeObject || activeObject.isGrid) {
            statusElement.innerHTML = '<i class="fas fa-hand-pointer"></i> Click on an image to select it';
            statusElement.classList.remove('selected', 'error');
        } else {
            statusElement.innerHTML = '<i class="fas fa-check-circle"></i> Image selected - ready to duplicate!';
            statusElement.classList.add('selected');
        }
    }

    /**
     * Duplicate selected objects
     */
    duplicateSelected() {
        const activeObject = this.canvas.getActiveObject();
        if (!activeObject || activeObject.isGrid) {
            this.showError('Please select an object to duplicate');
            return;
        }

        activeObject.clone((cloned) => {
            cloned.set({
                left: cloned.left + 20,
                top: cloned.top + 20,
                evented: true,
            });

            if (cloned.type === 'activeSelection') {
                // Handle multiple selection
                cloned.canvas = this.canvas;
                cloned.forEachObject((obj) => {
                    this.canvas.add(obj);
                });
                cloned.setCoords();
            } else {
                this.canvas.add(cloned);
            }

            this.canvas.setActiveObject(cloned);
            this.canvas.requestRenderAll();
            this.showSuccess('Object duplicated');
        });
    }

    /**
     * Mass duplicate selected object
     */
    massDuplicate(count) {
        console.log('massDuplicate called with count:', count);

        const activeObject = this.canvas.getActiveObject();
        console.log('Active object:', activeObject);

        // Check if there are any objects on canvas
        const allObjects = this.canvas.getObjects();
        const imageObjects = allObjects.filter(obj => !obj.isGrid);
        console.log('Total objects on canvas:', allObjects.length);
        console.log('Image objects on canvas:', imageObjects.length);

        if (imageObjects.length === 0) {
            this.showError('Please upload an image first');
            return;
        }

        if (!activeObject || activeObject.isGrid) {
            // If no object is selected, but there are images, select the first one
            if (imageObjects.length > 0) {
                this.canvas.setActiveObject(imageObjects[0]);
                this.canvas.renderAll();
                this.showInfo('Selected first image. Click "Create Selected Quantity" again to duplicate.');
                return;
            } else {
                this.showError('Please select an image to duplicate (click on an image first)');
                return;
            }
        }

        if (count < 1 || count > 500) {
            this.showError('Please enter a count between 1 and 500');
            return;
        }

        this.showInfo(`Creating ${count} copies...`);

        // Calculate spacing
        const spacingPixels = this.mmToPixels(this.imageSpacing);
        const marginPixels = this.mmToPixels(this.sheetMargins || 5);

        console.log('Spacing pixels:', spacingPixels, 'Margin pixels:', marginPixels);

        // Get canvas dimensions
        const canvasWidth = this.canvas.width - (marginPixels * 2);
        const canvasHeight = this.canvas.height - (marginPixels * 2);

        // Get object dimensions
        const objWidth = activeObject.getScaledWidth();
        const objHeight = activeObject.getScaledHeight();

        console.log('Object dimensions:', objWidth, 'x', objHeight);
        console.log('Available canvas area:', canvasWidth, 'x', canvasHeight);

        // Calculate how many fit per row
        const itemsPerRow = Math.floor((canvasWidth + spacingPixels) / (objWidth + spacingPixels));
        console.log('Items per row:', itemsPerRow);

        // Pre-calculate all positions
        const positions = [];
        let currentX = marginPixels;
        let currentY = marginPixels;

        for (let i = 0; i < count; i++) {
            // Check if we need to move to next row
            if (i > 0 && i % itemsPerRow === 0) {
                currentX = marginPixels;
                currentY += objHeight + spacingPixels;

                // Check if we've run out of vertical space
                if (currentY + objHeight > canvasHeight + marginPixels) {
                    console.log(`Only ${i} copies fit on the sheet`);
                    break;
                }
            }

            positions.push({ left: currentX, top: currentY });
            currentX += objWidth + spacingPixels;
        }

        console.log(`Will create ${positions.length} copies at calculated positions`);

        // Create clones with proper positioning
        let created = 0;
        const createNextClone = (index) => {
            if (index >= positions.length) {
                this.canvas.discardActiveObject();
                this.canvas.requestRenderAll();
                this.showSuccess(`Created ${created} copies with professional spacing`);
                return;
            }

            activeObject.clone((cloned) => {
                const pos = positions[index];
                cloned.set({
                    left: pos.left,
                    top: pos.top,
                    evented: true,
                });

                this.canvas.add(cloned);
                created++;

                console.log(`Created copy ${created} at position (${pos.left}, ${pos.top})`);

                // Create next clone
                createNextClone(index + 1);
            });
        };

        // Start creating clones
        createNextClone(0);
    }

    /**
     * Fill entire sheet with selected object
     */
    fillEntireSheet() {
        console.log('fillEntireSheet called');

        const activeObject = this.canvas.getActiveObject();
        console.log('Active object:', activeObject);

        // Check if there are any objects on canvas
        const allObjects = this.canvas.getObjects();
        const imageObjects = allObjects.filter(obj => !obj.isGrid);
        console.log('Total objects on canvas:', allObjects.length);
        console.log('Image objects on canvas:', imageObjects.length);

        if (imageObjects.length === 0) {
            this.showError('Please upload an image first');
            return;
        }

        if (!activeObject || activeObject.isGrid) {
            // If no object is selected, but there are images, select the first one
            if (imageObjects.length > 0) {
                this.canvas.setActiveObject(imageObjects[0]);
                this.canvas.renderAll();
                this.showInfo('Selected first image. Click "Fill Entire Sheet" again to continue.');
                return;
            } else {
                this.showError('Please select an image to fill the sheet with (click on an image first)');
                return;
            }
        }

        // Calculate how many copies will fit
        const spacingPixels = this.mmToPixels(this.imageSpacing);
        const marginPixels = this.mmToPixels(this.sheetMargins || 5);

        console.log('Canvas dimensions:', this.canvas.width, 'x', this.canvas.height);
        console.log('Spacing pixels:', spacingPixels, 'Margin pixels:', marginPixels);

        const canvasWidth = this.canvas.width - (marginPixels * 2);
        const canvasHeight = this.canvas.height - (marginPixels * 2);

        const objWidth = activeObject.getScaledWidth();
        const objHeight = activeObject.getScaledHeight();

        console.log('Object dimensions:', objWidth, 'x', objHeight);
        console.log('Available canvas area:', canvasWidth, 'x', canvasHeight);

        const itemsPerRow = Math.floor((canvasWidth + spacingPixels) / (objWidth + spacingPixels));
        const rowsPerSheet = Math.floor((canvasHeight + spacingPixels) / (objHeight + spacingPixels));

        console.log('Items per row:', itemsPerRow, 'Rows per sheet:', rowsPerSheet);

        const totalItems = itemsPerRow * rowsPerSheet;
        console.log('Total items that will fit:', totalItems);

        if (totalItems <= 1) {
            this.showError('Object is too large to duplicate on this sheet size');
            return;
        }

        const confirmed = confirm(`This will create ${totalItems} copies to fill the entire sheet. Continue?`);
        if (!confirmed) return;

        console.log('Starting mass duplicate with', totalItems - 1, 'copies');
        this.massDuplicate(totalItems - 1); // -1 because original is already on canvas
    }

    /**
     * Delete selected objects
     */
    deleteSelected() {
        const activeObject = this.canvas.getActiveObject();
        if (!activeObject || activeObject.isGrid) {
            this.showError('Please select an object to delete');
            return;
        }

        if (activeObject.type === 'activeSelection') {
            // Handle multiple selection
            activeObject.forEachObject((obj) => {
                this.canvas.remove(obj);
            });
        } else {
            this.canvas.remove(activeObject);
        }

        this.canvas.discardActiveObject();
        this.canvas.requestRenderAll();
        this.showSuccess('Object deleted');
    }

    /**
     * Add crop marks to canvas
     */
    addCropMarks() {
        if (!this.addCropMarks) return;

        const canvasWidth = this.canvas.width;
        const canvasHeight = this.canvas.height;
        const markLength = 20;
        const markOffset = 10;

        // Remove existing crop marks
        this.removeCropMarks();

        // Create crop mark lines
        const cropMarks = [];

        // Top-left corner
        cropMarks.push(
            new fabric.Line([markOffset, markOffset + markLength, markOffset, markOffset], {
                stroke: '#000',
                strokeWidth: 1,
                selectable: false,
                evented: false,
                isCropMark: true
            }),
            new fabric.Line([markOffset, markOffset, markOffset + markLength, markOffset], {
                stroke: '#000',
                strokeWidth: 1,
                selectable: false,
                evented: false,
                isCropMark: true
            })
        );

        // Top-right corner
        cropMarks.push(
            new fabric.Line([canvasWidth - markOffset, markOffset + markLength, canvasWidth - markOffset, markOffset], {
                stroke: '#000',
                strokeWidth: 1,
                selectable: false,
                evented: false,
                isCropMark: true
            }),
            new fabric.Line([canvasWidth - markOffset, markOffset, canvasWidth - markOffset - markLength, markOffset], {
                stroke: '#000',
                strokeWidth: 1,
                selectable: false,
                evented: false,
                isCropMark: true
            })
        );

        // Bottom-left corner
        cropMarks.push(
            new fabric.Line([markOffset, canvasHeight - markOffset - markLength, markOffset, canvasHeight - markOffset], {
                stroke: '#000',
                strokeWidth: 1,
                selectable: false,
                evented: false,
                isCropMark: true
            }),
            new fabric.Line([markOffset, canvasHeight - markOffset, markOffset + markLength, canvasHeight - markOffset], {
                stroke: '#000',
                strokeWidth: 1,
                selectable: false,
                evented: false,
                isCropMark: true
            })
        );

        // Bottom-right corner
        cropMarks.push(
            new fabric.Line([canvasWidth - markOffset, canvasHeight - markOffset - markLength, canvasWidth - markOffset, canvasHeight - markOffset], {
                stroke: '#000',
                strokeWidth: 1,
                selectable: false,
                evented: false,
                isCropMark: true
            }),
            new fabric.Line([canvasWidth - markOffset, canvasHeight - markOffset, canvasWidth - markOffset - markLength, canvasHeight - markOffset], {
                stroke: '#000',
                strokeWidth: 1,
                selectable: false,
                evented: false,
                isCropMark: true
            })
        );

        // Add all crop marks to canvas
        cropMarks.forEach(mark => this.canvas.add(mark));
        this.canvas.renderAll();
    }

    /**
     * Remove crop marks from canvas
     */
    removeCropMarks() {
        const objects = this.canvas.getObjects();
        const cropMarks = objects.filter(obj => obj.isCropMark);
        cropMarks.forEach(mark => this.canvas.remove(mark));
    }

    /**
     * Apply professional spacing between objects
     */
    applyProfessionalSpacing() {
        const objects = this.canvas.getObjects().filter(obj => !obj.isGrid && !obj.isCropMark);
        if (objects.length < 2) return;

        const spacingPixels = this.mmToPixels(this.imageSpacing);

        // Sort objects by position
        objects.sort((a, b) => {
            if (Math.abs(a.top - b.top) < 10) {
                return a.left - b.left;
            }
            return a.top - b.top;
        });

        // Apply spacing
        let currentX = this.safetyMargins ? this.mmToPixels(5) : 0;
        let currentY = this.safetyMargins ? this.mmToPixels(5) : 0;
        let rowHeight = 0;

        objects.forEach(obj => {
            // Check if object fits in current row
            if (currentX + obj.getScaledWidth() > this.canvas.width - (this.safetyMargins ? this.mmToPixels(5) : 0)) {
                // Move to next row
                currentX = this.safetyMargins ? this.mmToPixels(5) : 0;
                currentY += rowHeight + spacingPixels;
                rowHeight = 0;
            }

            obj.set({
                left: currentX,
                top: currentY
            });

            currentX += obj.getScaledWidth() + spacingPixels;
            rowHeight = Math.max(rowHeight, obj.getScaledHeight());
        });

        this.canvas.renderAll();
    }

    /**
     * Convert millimeters to pixels
     */
    mmToPixels(mm) {
        // Assuming 300 DPI and 25.4 mm per inch
        return (mm / 25.4) * 300 * (this.canvas.width / (this.sheetDimensions?.width || 30));
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Check if Fabric.js is loaded
    if (typeof fabric === 'undefined') {
        console.error('Fabric.js is not loaded. Please include the Fabric.js library.');
        return;
    }

    window.dtfBuilder = new DTFGangBuilder();
});
